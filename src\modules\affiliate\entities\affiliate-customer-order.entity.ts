import { Column, <PERSON>tity, <PERSON><PERSON><PERSON><PERSON>umn, ManyToOne, PrimaryColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { AffiliateAccount } from './affiliate-account.entity';

/**
 * Entity đại diện cho bảng affiliate_customer_order trong cơ sở dữ liệu
 * Đơn hàng affiliate
 */
@Entity('affiliate_customer_order')
export class AffiliateCustomerOrder {
  /**
   * Mã đơn hàng
   */
  @PrimaryColumn({ name: 'order_id', type: 'bigint', comment: 'Mã đơn hàng' })
  orderId: number;

  /**
   * Phần trăm hoa hồng
   */
  @Column({
    name: 'commission',
    type: 'float',
    nullable: true,
    comment: 'Phần trăm hoa hồng'
  })
  commission: number;

  /**
   * ID tài khoản affiliate
   */
  @Column({
    name: 'affiliate_account_id',
    comment: 'tài khoản affiliate'
  })
  affiliateAccountId: number;

  /**
   * Mã rank affiliate không cần nối khóa phụ
   */
  @Column({
    name: 'rank_id',
    type: 'integer',
    nullable: true,
    comment: 'Mã rank affiliate không cần nối khóa phụ'
  })
  rankId: number;

  /**
   * Số tiền đơn hàng
   */
  @Column({
    name: 'amount',
    type: 'float',
    nullable: true,
    comment: 'Số tiền đơn hàng'
  })
  amount: number;

  /**
   * Số tiền hoa hồng
   */
  @Column({
    name: 'commission_amount',
    type: 'float',
    nullable: true,
    comment: 'Số tiền hoa hồng'
  })
  commissionAmount: number;

  /**
   * ID khách hàng
   */
  @Column({
    name: 'customer_id',
    type: 'integer',
    nullable: true,
    comment: 'ID khách hàng'
  })
  customerId: number;

  /**
   * Thời gian tạo đơn hàng (Unix timestamp)
   */
  @CreateDateColumn({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)',
    comment: 'Thời gian tạo đơn hàng (Unix timestamp)'
  })
  createdAt: number;

  /**
   * Thời gian cập nhật đơn hàng (Unix timestamp)
   */
  @UpdateDateColumn({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)',
    comment: 'Thời gian cập nhật đơn hàng (Unix timestamp)'
  })
  updatedAt: number;
}
