### Test API lấy danh sách loại tích hợp của người dùng

### API: GET /v1/user/integration/types
### Mô tả: L<PERSON><PERSON> danh sách các loại tích hợp mà người dùng đã tích hợp
### Authentication: <PERSON><PERSON> (JWT)

GET http://localhost:3000/v1/user/integration/types
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

### Expected Response:
### {
###   "code": 200,
###   "message": "L<PERSON>y danh sách loại tích hợp thành công",
###   "result": [
###     {
###       "type": "ANALYTICS",
###       "count": 2,
###       "latestCreatedAt": "2025-06-14T03:50:44.773Z"
###     },
###     {
###       "type": "EMAIL_SMTP", 
###       "count": 1,
###       "latestCreatedAt": "2025-06-13T10:30:00.000Z"
###     },
###     {
###       "type": "SMS",
###       "count": 3,
###       "latestCreatedAt": "2025-06-12T15:20:30.000Z"
###     },
###     {
###       "type": "FACEBOOK_PAGE",
###       "count": 1,
###       "latestCreatedAt": "2025-06-11T08:45:15.000Z"
###     }
###   ]
### }

### Các loại tích hợp có thể có:
### - FACEBOOK_PAGE: Tích hợp Facebook Page
### - MB_BANK_SEPAY_HUB: Tích hợp MB Bank qua Sepay Hub  
### - OCB_BANK_SEPAY_HUB: Tích hợp OCB Bank qua Sepay Hub
### - ACB_BANK_SEPAY_HUB: Tích hợp ACB Bank qua Sepay Hub
### - EMAIL_SMTP: Tích hợp Email SMTP
### - SMS: Tích hợp SMS chung
### - SMS TWILIO: Tích hợp SMS Twilio
### - SMS FPT: Tích hợp SMS FPT
### - ANALYTICS: Tích hợp Analytics
### - SHIPPING: Tích hợp Shipping/Vận chuyển

### Response Fields:
### - type: Loại tích hợp (enum IntegrationTypeEnum)
### - count: Số lượng tích hợp của loại này mà user đã tạo
### - latestCreatedAt: Thời điểm tạo tích hợp gần nhất của loại này

### Use Cases:
### 1. Hiển thị dashboard tổng quan các tích hợp của user
### 2. Thống kê số lượng tích hợp theo từng loại
### 3. Hiển thị thời gian tích hợp gần nhất
### 4. Kiểm tra user đã tích hợp loại nào chưa
