### Test API lấy danh sách loại tích hợp duy nhất của người dùng

### API: GET /v1/user/integration/types-list
### Mô tả: <PERSON><PERSON><PERSON> tất cả tích hợp của user và trả về danh sách các type duy nhất (không trùng lặp)
### Authentication: <PERSON><PERSON> (JWT)

GET http://localhost:3000/v1/user/integration/types-list
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

### Expected Response:
### {
###   "code": 200,
###   "message": "L<PERSON>y danh sách loại tích hợp duy nhất thành công",
###   "result": [
###     {
###       "type": "ANALYTICS"
###     },
###     {
###       "type": "EMAIL_SMTP"
###     },
###     {
###       "type": "SMS"
###     },
###     {
###       "type": "FACEBOOK_PAGE"
###     }
###   ]
### }

### So sánh với API /types:
### API /types trả về thông tin chi tiết:
### {
###   "type": "ANALYTICS",
###   "count": 2,
###   "latestCreatedAt": "2025-06-14T03:50:44.773Z"
### }
###
### API /types-list chỉ trả về type:
### {
###   "type": "ANALYTICS"
### }

### Logic hoạt động:
### 1. Lấy tất cả tích hợp của user (limit 1000)
### 2. Extract field 'type' từ mỗi tích hợp
### 3. Loại bỏ các type trùng lặp bằng Set
### 4. Chuyển đổi thành DTO response

### Use Cases:
### 1. Hiển thị dropdown/select các loại tích hợp user đã có
### 2. Filter/search theo loại tích hợp
### 3. Kiểm tra nhanh user đã tích hợp loại nào
### 4. UI đơn giản chỉ cần hiển thị tên loại tích hợp

### Ưu điểm so với API /types:
### - Response nhẹ hơn (không có count, latestCreatedAt)
### - Phù hợp cho dropdown/select
### - Tốc độ nhanh hơn khi chỉ cần danh sách type
