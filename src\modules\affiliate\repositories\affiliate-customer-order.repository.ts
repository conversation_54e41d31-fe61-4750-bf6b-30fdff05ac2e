import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { AffiliateCustomerOrder } from '../entities/affiliate-customer-order.entity';
import { AffiliateOrderQueryDto } from '../user/dto';
import { PaginatedResult } from '@/common/response';

/**
 * Repository cho AffiliateCustomerOrder
 * Extends Repository<AffiliateCustomerOrder> theo Repository Standard #2
 */
@Injectable()
export class AffiliateCustomerOrderRepository extends Repository<AffiliateCustomerOrder> {
  constructor(dataSource: DataSource) {
    super(AffiliateCustomerOrder, dataSource.createEntityManager());
  }

  /**
   * Đếm số đơn hàng theo tài khoản affiliate và khoảng thời gian
   * @param affiliateAccountId ID tài khoản affiliate
   * @param begin Thời gian bắt đầu
   * @param end Thời gian kết thúc
   * @returns Số đơn hàng
   */
  async countByAffiliateAccountIdAndTimeRange(
    affiliateAccountId: number,
    begin: number,
    end: number,
  ): Promise<number> {
    return this.createQueryBuilder('order')
      .where('order.affiliateAccountId = :affiliateAccountId', {
        affiliateAccountId,
      })
      .andWhere('order.createdAt >= :begin', { begin })
      .andWhere('order.createdAt <= :end', { end })
      .getCount();
  }

  /**
   * Tính tổng doanh thu theo tài khoản affiliate và khoảng thời gian
   * @param affiliateAccountId ID tài khoản affiliate
   * @param begin Thời gian bắt đầu
   * @param end Thời gian kết thúc
   * @returns Tổng doanh thu
   */
  async calculateRevenueByAffiliateAccountIdAndTimeRange(
    affiliateAccountId: number,
    begin: number,
    end: number,
  ): Promise<number> {
    const result = await this.createQueryBuilder('order')
      .select('SUM(order.amount)', 'total')
      .where('order.affiliateAccountId = :affiliateAccountId', {
        affiliateAccountId,
      })
      .andWhere('order.createdAt >= :begin', { begin })
      .andWhere('order.createdAt <= :end', { end })
      .getRawOne();

    return parseFloat(result?.total) || 0;
  }

  /**
   * Tìm danh sách đơn hàng với phân trang
   * @param affiliateAccountId ID tài khoản affiliate
   * @param queryDto Tham số truy vấn
   * @returns Danh sách đơn hàng với phân trang
   */
  async findWithPagination(
    affiliateAccountId: number,
    queryDto: AffiliateOrderQueryDto,
  ): Promise<PaginatedResult<AffiliateCustomerOrder>> {
    const {
      page = 1,
      limit = 10,
      begin,
      end,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = queryDto;

    const skip = (page - 1) * limit;

    // Xây dựng query - sử dụng alias 'o' thay vì 'order' để tránh xung đột với từ khóa SQL
    // Join với PointPurchaseTransaction để lấy amount và customerId
    const queryBuilder = this.createQueryBuilder('o')
      .leftJoin('point_purchase_transactions', 'ppt', 'o.order_id = ppt.id')
      .addSelect('ppt.amount', 'amount')
      .addSelect('ppt.user_id', 'customerId')
      .where('o.affiliateAccountId = :affiliateAccountId', {
        affiliateAccountId,
      });

    // Thêm điều kiện thời gian nếu có - sử dụng createdAt từ PointPurchaseTransaction
    if (begin) {
      queryBuilder.andWhere('ppt.created_at >= :begin', { begin });
    }

    if (end) {
      queryBuilder.andWhere('ppt.created_at <= :end', { end });
    }

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.andWhere('o.orderId LIKE :search', {
        search: `%${search}%`,
      });
    }

    // Đếm tổng số bản ghi
    const totalItems = await queryBuilder.getCount();

    // Thêm sắp xếp và phân trang
    // Nếu sort theo createdAt thì dùng từ PointPurchaseTransaction
    const sortColumn = sortBy === 'createdAt' ? 'ppt.created_at' : `o.${sortBy}`;
    queryBuilder
      .orderBy(sortColumn, sortDirection)
      .skip(skip)
      .take(limit);

    // Lấy dữ liệu với thông tin từ join
    const { entities, raw } = await queryBuilder.getRawAndEntities();

    // Map dữ liệu từ join vào entities
    const items = entities.map((entity, index) => ({
      ...entity,
      amount: raw[index]?.amount || 0,
      customerId: raw[index]?.customerId || 0,
    }));

    // Tính toán metadata
    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Đếm số đơn hàng theo tài khoản affiliate và khách hàng
   * @param affiliateAccountId ID tài khoản affiliate
   * @param customerId ID khách hàng
   * @param begin Thời gian bắt đầu
   * @param end Thời gian kết thúc
   * @returns Số đơn hàng
   */
  async countByAffiliateAccountIdAndCustomerId(
    affiliateAccountId: number,
    customerId: number,
    begin: number,
    end: number,
  ): Promise<number> {
    return this.createQueryBuilder('o')
      .leftJoin('point_purchase_transactions', 'ppt', 'o.order_id = ppt.id')
      .where('o.affiliateAccountId = :affiliateAccountId', {
        affiliateAccountId,
      })
      .andWhere('ppt.user_id = :customerId', { customerId })
      .andWhere('ppt.created_at >= :begin', { begin })
      .andWhere('ppt.created_at <= :end', { end })
      .getCount();
  }

  /**
   * Tính tổng chi tiêu theo tài khoản affiliate và khách hàng
   * @param affiliateAccountId ID tài khoản affiliate
   * @param customerId ID khách hàng
   * @param begin Thời gian bắt đầu
   * @param end Thời gian kết thúc
   * @returns Tổng chi tiêu
   */
  async calculateTotalSpentByAffiliateAccountIdAndCustomerId(
    affiliateAccountId: number,
    customerId: number,
    begin: number,
    end: number,
  ): Promise<number> {
    const result = await this.createQueryBuilder('o')
      .leftJoin('point_purchase_transactions', 'ppt', 'o.order_id = ppt.id')
      .select('SUM(ppt.amount)', 'total')
      .where('o.affiliateAccountId = :affiliateAccountId', {
        affiliateAccountId,
      })
      .andWhere('ppt.user_id = :customerId', { customerId })
      .andWhere('ppt.created_at >= :begin', { begin })
      .andWhere('ppt.created_at <= :end', { end })
      .getRawOne();

    return parseFloat(result?.total) || 0;
  }

  /**
   * Đếm tổng số đơn hàng
   * @returns Tổng số đơn hàng
   */
  async countTotal(): Promise<number> {
    return this.count();
  }

  /**
   * Tính tổng doanh thu và hoa hồng
   * @returns Tổng doanh thu và hoa hồng
   */
  async calculateTotalRevenueAndCommission(): Promise<{
    totalRevenue: number;
    totalCommission: number;
  }> {
    const result = await this.createQueryBuilder('o')
      .leftJoin('point_purchase_transactions', 'ppt', 'o.order_id = ppt.id')
      .select('SUM(ppt.amount)', 'totalRevenue')
      .addSelect('SUM(o.commissionAmount)', 'totalCommission')
      .getRawOne();

    return {
      totalRevenue: parseFloat(result?.totalRevenue) || 0,
      totalCommission: parseFloat(result?.totalCommission) || 0,
    };
  }

  /**
   * Tính toán thống kê theo rank ID
   * @param rankId ID của rank
   * @returns Thống kê doanh thu, hoa hồng và số đơn hàng
   */
  async calculateStatsByRankId(rankId: number): Promise<{ totalRevenue: number; totalCommission: number; orderCount: number }> {
    // Đếm số đơn hàng theo rank_id
    // Sử dụng "o" thay vì "order" làm alias để tránh xung đột với từ khóa SQL
    const orderCount = await this
      .createQueryBuilder('o')
      .where('o.rankId = :rankId', { rankId })
      .getCount();

    // Trả về kết quả mặc định vì không có dữ liệu thực tế về doanh thu và hoa hồng
    // Trong thực tế, bạn cần lấy dữ liệu này từ bảng khác hoặc tính toán dựa trên logic nghiệp vụ
    return {
      totalRevenue: 0,
      totalCommission: 0,
      orderCount
    };
  }
}
