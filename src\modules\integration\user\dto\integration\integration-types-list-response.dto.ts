import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IntegrationTypeEnum } from '@modules/integration/enums';

/**
 * DTO response cho danh sách loại tích hợp đơn giản
 */
export class IntegrationTypesListResponseDto {
  /**
   * <PERSON><PERSON><PERSON> tích hợp
   * @example "ANALYTICS"
   */
  @ApiProperty({
    description: 'Loại tích hợp',
    enum: IntegrationTypeEnum,
    example: IntegrationTypeEnum.ANALYTICS
  })
  @Expose()
  type: IntegrationTypeEnum;
}
