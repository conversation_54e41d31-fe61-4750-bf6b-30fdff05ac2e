### Test Reset Password API
### Bước 1: <PERSON><PERSON><PERSON><PERSON> mật khẩu - <PERSON><PERSON><PERSON> cầu OTP
POST http://localhost:3000/v1/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}

### Bước 2: <PERSON><PERSON><PERSON> thực OTP quên mật khẩu
POST http://localhost:3000/v1/auth/verify-forgot-password
Content-Type: application/json

{
  "otp": "123456",
  "otpToken": "YOUR_OTP_TOKEN_FROM_STEP_1"
}

### Bước 3: Đặt lại mật khẩu mới (sẽ tr<PERSON> về token như đăng nhập)
POST http://localhost:3000/v1/auth/reset-password
Content-Type: application/json

{
  "newPassword": "newPassword123",
  "changePasswordToken": "YOUR_CHANGE_PASSWORD_TOKEN_FROM_STEP_2"
}

### Expected Response:
### {
###   "code": 200,
###   "message": "<PERSON><PERSON><PERSON> mật khẩu thành công",
###   "result": {
###     "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
###     "expiresIn": 604800,
###     "expiresAt": 1746968772000,
###     "info": [],
###     "user": {
###       "id": 1,
###       "email": "<EMAIL>",
###       "username": "",
###       "permissions": ["user:read"],
###       "status": "active"
###     }
###   }
### }
### 
### Refresh token sẽ được set trong cookie HTTP-only
### Email thông báo đổi mật khẩu thành công sẽ được gửi đến user
