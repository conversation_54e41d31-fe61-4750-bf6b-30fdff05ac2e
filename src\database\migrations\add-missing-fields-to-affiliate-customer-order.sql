-- Migration: <PERSON>h<PERSON><PERSON> các field thiếu vào bảng affiliate_customer_order
-- Date: 2025-06-20
-- Description: Thêm amount, commission_amount, customer_id, created_at, updated_at vào bảng affiliate_customer_order

-- Thêm cột amount (số tiền đơn hàng)
ALTER TABLE affiliate_customer_order
ADD COLUMN IF NOT EXISTS amount FLOAT NULL;

COMMENT ON COLUMN affiliate_customer_order.amount
IS 'Số tiền đơn hàng';

-- Thêm cột commission_amount (số tiền hoa hồng)
ALTER TABLE affiliate_customer_order
ADD COLUMN IF NOT EXISTS commission_amount FLOAT NULL;

COMMENT ON COLUMN affiliate_customer_order.commission_amount
IS 'Số tiền hoa hồng';

-- Thêm cột customer_id (ID khách hàng)
ALTER TABLE affiliate_customer_order
ADD COLUMN IF NOT EXISTS customer_id INTEGER NULL;

COMMENT ON COLUMN affiliate_customer_order.customer_id
IS 'ID khách hàng';

-- Thê<PERSON> cột created_at (thời gian tạo)
ALTER TABLE affiliate_customer_order
ADD COLUMN IF NOT EXISTS created_at BIGINT NULL DEFAULT ((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT);

COMMENT ON COLUMN affiliate_customer_order.created_at
IS 'Thời gian tạo đơn hàng (Unix timestamp)';

-- Thêm cột updated_at (thời gian cập nhật)
ALTER TABLE affiliate_customer_order
ADD COLUMN IF NOT EXISTS updated_at BIGINT NULL DEFAULT ((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT);

COMMENT ON COLUMN affiliate_customer_order.updated_at
IS 'Thời gian cập nhật đơn hàng (Unix timestamp)';

-- Cập nhật dữ liệu hiện có với timestamp hiện tại nếu NULL
UPDATE affiliate_customer_order 
SET created_at = ((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)
WHERE created_at IS NULL;

UPDATE affiliate_customer_order 
SET updated_at = ((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)
WHERE updated_at IS NULL;
