import { NestFactory } from '@nestjs/core';
import { AppModule } from '@app';
import { SwaggerModule } from '@nestjs/swagger';
import { VersioningType } from '@nestjs/common';
import { createSwaggerConfig, swaggerCustomOptions } from '@common/swagger';
import { ConfigService } from '@nestjs/config';
import * as dotenv from 'dotenv';
import { initializeTypeOrmTransactional } from '@config/typeorm-transactional.config';
import { corsConfig } from '@common/filters/cors.config';
import * as bodyParser from 'body-parser';
import { CustomValidationPipe } from '@common/pipes/custom-validation.pipe';

// Khởi tạo typeorm-transactional
initializeTypeOrmTransactional();

dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // C<PERSON>u hình giới hạn kích thước request body với x<PERSON> lý JSON an toàn
  app.use(bodyParser.json({
    limit: '50mb',
    verify: (req: any, res: any, buf: Buffer) => {
      try {
        const jsonString = buf.toString('utf8');
        // Kiểm tra và log chi tiết nếu có ký tự điều khiển
        const controlChars = jsonString.match(/[\x00-\x1F\x7F]/g);
        if (controlChars) {
          console.warn('Found control characters in JSON:', {
            chars: controlChars.map(c => `\\x${c.charCodeAt(0).toString(16).padStart(2, '0')}`),
            positions: controlChars.map(c => jsonString.indexOf(c)),
            url: req.url
          });
        }

        // Thử parse JSON gốc trước
        JSON.parse(jsonString);
      } catch (error) {
        console.error('JSON parsing error details:', {
          error: error.message,
          url: req.url,
          method: req.method,
          contentType: req.headers['content-type'],
          bodyLength: buf.length,
          bodyPreview: buf.toString('utf8').substring(0, 500)
        });

        // Thử làm sạch và parse lại
        try {
          const cleanedJson = buf.toString('utf8').replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
          JSON.parse(cleanedJson);
          console.log('Successfully cleaned and parsed JSON');
        } catch (cleanError) {
          console.error('Even cleaned JSON failed to parse:', cleanError.message);
        }

        throw error;
      }
    }
  }));
  app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

  // app.setGlobalPrefix('api');

  // Thêm global prefix với version
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  })

  // Cấu hình custom validation pipe
  app.useGlobalPipes(new CustomValidationPipe());

  // Cấu hình CORS với danh sách các frontend được phép
  app.enableCors(corsConfig);

  // Cấu hình Swagger với thông tin từ biến môi trường
  const configService = app.get(ConfigService);
  const swaggerConfig = createSwaggerConfig(configService);
  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup('api/docs', app, document, swaggerCustomOptions);

  // Khởi động server
  const port = process.env.PORT ?? 3000;
  await app.listen(port);
  console.log(`Application is running on: http://localhost:${port}`);
  console.log(`Swagger documentation is available at: http://localhost:${port}/api/docs`);
}
bootstrap().catch((err) => {
  console.error(err);
  process.exit(1);
});
