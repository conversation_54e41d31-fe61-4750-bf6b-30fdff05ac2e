import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của API gửi lại OTP quên mật khẩu
 */
export class ResendForgotPasswordOtpResponseDto {
  @ApiProperty({
    description: 'Token OTP mới để xác thực',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  otpToken: string;

  @ApiProperty({
    description: 'Thời điểm hết hạn của token OTP (timestamp)',
    example: 1746968772000,
  })
  expiresAt: number;

  @ApiProperty({
    description: 'Email đã được che một phần',
    example: 'u***@example.com',
  })
  maskedEmail: string;

  @ApiProperty({
    description: 'Mã OTP (chỉ hiển thị trong môi trường phát triển)',
    example: '123456',
    required: false,
  })
  otp?: string;
}
